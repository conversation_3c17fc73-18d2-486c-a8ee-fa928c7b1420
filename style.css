/*
Theme Name: CelebNews
Theme URI:  https://afthemes.com/products/celebnews/
Author:     AF themes
Author URI: https://afthemes.com/
Description: CelebNews is a modern, mobile-optimized WordPress theme crafted for musicians, bands, singers, celebrity blogs, entertainment magazines, lifestyle news portals, and multimedia websites. With flexible layouts, responsive design, and bold styling, it’s ideal for showbiz coverage, pop culture news, and artist portfolios. CelebNews is fully compatible with block editors and popular page builders like Elementor, Gutenberg, Brizy, Beaver Builder, Visual Composer, and Divi—offering intuitive drag-and-drop customization. Built with performance, accessibility, and GDPR-readiness in mind, the theme is fast-loading, SEO-optimized, AMP-ready, and developed with clean, lightweight code. It includes custom widgets, banner-ready layouts, social media integration, WooCommerce support, and compatibility with Jetpack, Contact Form 7, and Yoast SEO. Designed for a global audience, CelebNews features 1-click demo import and multilingual starter content in Spanish, German, French, Portuguese, Russian, Italian, Japanese, Dutch, Arabic, and more. With full translation and RTL language support, CelebNews is the perfect all-in-one solution for news portals, entertainment blogs, music magazines, and content-driven websites that demand both style and speed. Explore more at https://afthemes.com/products/celebnews/.
Template: morenews
Version: 1.1.0
Requires at least: 4.0
Requires PHP: 5.3
Tested up to: 6.8
License: GNU General Public License v2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
Text Domain: celebnews
Tags: news, blog, entertainment, one-column, two-columns, three-columns, four-columns, grid-layout, block-patterns, block-styles, left-sidebar, right-sidebar, custom-header, flexible-header, custom-background, custom-logo, custom-menu, custom-colors, featured-images, full-width-template, post-formats, rtl-language-support, footer-widgets, translation-ready, theme-options, threaded-comments, wide-blocks

This theme, like WordPress, is licensed under the GPL.
Use it to make something cool, have fun, and share what you've learned with others.

CelebNews WordPress Theme, Copyright 2025 AF themes
CelebNews is distributed under the terms of the GNU GPL v2 or later.
*/

div#main-navigation-bar {
    background: #D1A74F;
}



body:not(.home) .header-layout-compressed-full .full-width.af-transparent-head .af-for-transparent .main-navigation .menu>ul>li>a,
body .header-layout-compressed .compress-bar-mid .date-bar-mid,
body .main-navigation ul.menu>li>a,
body .main-navigation ul li a,
body.aft-dark-mode .main-navigation ul li a:hover,
body .morenews-header .search-icon:visited,
body .morenews-header .search-icon:hover,
body .morenews-header .search-icon:focus,
body .morenews-header .search-icon:active,
body .morenews-header .search-icon {
    color: #1A1A1A;
}

body .header-layout-side .offcanvas-menu span,
body .header-layout-centered .offcanvas-menu span,
body .ham:before,
body .ham:after,
body .ham {
    background-color: #1A1A1A;
}

@media screen and (max-width: 990px) {
    body .morenews-header.header-layout-centered .search-watch.aft-show-on-mobile .search-icon {
        color: #1A1A1A;
    }

    .header-layout-centered .main-navigation .toggle-menu a,
    .header-layout-side .main-navigation .toggle-menu a,
    .header-layout-compressed-full .main-navigation .toggle-menu a {
        outline-color: #1A1A1A;
    }
}



.exclusive-posts .exclusive-now,
.morenews-header.header-layout-centered .top-bar-right div.custom-menu-link>a,
.morenews-header.header-layout-compressed-full .top-bar-right div.custom-menu-link>a,
.morenews-header.header-layout-side .search-watch div.custom-menu-link>a {
    background: #D81E2C;
}

.exclusive-posts .exclusive-now {
    background: #D81E2C;
}

.main-navigation .menu-description {
    background-color: #D81E2C;
}

.main-navigation .menu-description:after {
    border-top: 5px solid #D81E2C;
}

.morenews-header div.custom-menu-link>a {
    background: #D81E2C;
}

.aft-dark-mode .aft-main-banner-wrapper .af-slick-navcontrols,
.aft-dark-mode .morenews-widget .af-slick-navcontrols,
.aft-dark-mode .morenews-customizer .section-wrapper .af-slick-navcontrols,

body.aft-dark-mode.single-post-title-full .entry-header-details,
body.aft-dark-mode .main-navigation .menu .menu-mobile,
body.aft-dark-mode .main-navigation .menu>ul>li>ul,
body.aft-dark-mode .main-navigation .menu>ul ul,
body.aft-dark-mode .af-search-form,
body.aft-dark-mode .aft-popular-taxonomies-lists,
body.aft-dark-mode .exclusive-slides::before,
body.aft-dark-mode .exclusive-slides::after,
body.aft-dark-mode .banner-exclusive-posts-wrapper .exclusive-posts:before,

body.aft-dark-mode.woocommerce div.product,
body.aft-dark-mode.home.blog main.site-main,
body.aft-dark-mode main.site-main,
body.aft-dark-mode.single main.site-main .entry-content-wrap,
body.aft-dark-mode .af-main-banner-latest-posts.grid-layout.morenews-customizer .container-wrapper,
body.aft-dark-mode .af-middle-header,
body.aft-dark-mode .mid-header-wrapper,
body.aft-dark-mode .comments-area,
body.aft-dark-mode .af-breadcrumbs,
.aft-dark-mode .morenews-customizer,
body.aft-dark-mode .morenews-widget {
    background-color: #1A1A1A;
}

.af-cat-widget-carousel a.morenews-categories.category-color-1 {
    background-color: #007833;

}

a.morenews-categories.category-color-1 {
    color: #007833;
}

.af-cat-widget-carousel a.morenews-categories.category-color-2 {
    background-color: #D1A74F;

}

.categories-inside-image a.morenews-categories.category-color-2 {
    color: #1A1A1A;
}

a.morenews-categories.category-color-2 {
    color: #D1A74F;
}


.woocommerce #respond input#submit.disabled,
.woocommerce #respond input#submit:disabled,
.woocommerce #respond input#submit:disabled[disabled],
.woocommerce a.button.disabled,
.woocommerce a.button:disabled,
.woocommerce a.button:disabled[disabled],
.woocommerce button.button.disabled,
.woocommerce button.button:disabled,
.woocommerce button.button:disabled[disabled],
.woocommerce input.button.disabled,
.woocommerce input.button:disabled,
.woocommerce input.button:disabled[disabled],
.woocommerce #respond input#submit,
.woocommerce a.button,
body .entry-content>[class*="wp-block-"] .woocommerce a:not(.has-text-color).button,
.woocommerce button.button,
.woocommerce input.button,
.woocommerce #respond input#submit.alt,
.woocommerce a.button.alt,
.woocommerce button.button.alt,
.woocommerce input.button.alt,
.woocommerce-account .addresses .title .edit,
.wp-block-button.wc-block-components-product-button .wp-block-button__link,
.wc-block-grid__product-add-to-cart.wp-block-button .wp-block-button__link,
.wc-block-grid .wp-block-button__link,
.wc-block-grid .wp-block-button__link:visited,
.wc-block-grid .wp-block-button__link:hover,
body.aft-default-mode .woocommerce-notices-wrapper .button:hover,
body.aft-dark-mode .woocommerce-notices-wrapper .button:hover,
.woocommerce-notices-wrapper .button,
.aft-dark-mode .entry-content a.woocommerce-button.view,
.aft-dark-mode .entry-content a.woocommerce-button.view:hover,
body.woocommerce a.button.add_to_cart_button:hover,
:root .wc-block-featured-product__link :where(.wp-element-button, .wp-block-button__link),
:root .wc-block-featured-product__link :where(.wp-element-button:hover, .wp-block-button__link:hover),
:root .wc-block-featured-category__link :where(.wp-element-button, .wp-block-button__link),
:root .wc-block-featured-category__link :where(.wp-element-button:hover, .wp-block-button__link:hover),
body .hustle-button,
body .hustle-button:hover,

body .morenews-pagination .nav-links .page-numbers.current,
body.aft-default-mode .grid-design-texts-over-image .aft-readmore-wrapper a.aft-readmore:hover,
.grid-design-texts-over-image .aft-readmore-wrapper a.aft-readmore:hover,
body.aft-dark-mode .grid-design-texts-over-image .aft-readmore-wrapper a.aft-readmore:hover,
.aft-readmore-wrapper a.aft-readmore:hover,
body.aft-dark-mode .aft-readmore-wrapper a.aft-readmore:hover,
body.aft-default-mode .aft-readmore-wrapper a.aft-readmore:hover,
footer.site-footer .aft-readmore-wrapper a.aft-readmore:hover,
body.aft-default-mode .reply a,
body.aft-dark-mode .reply a,

.widget-title-fill-and-border .wp-block-search__label,
.widget-title-fill-and-border .wp-block-group .wp-block-heading,
.widget-title-fill-and-no-border .wp-block-search__label,
.widget-title-fill-and-no-border .wp-block-group .wp-block-heading,

.widget-title-fill-and-border .wp_post_author_widget .widget-title .header-after,
.widget-title-fill-and-border .widget-title .heading-line,
.widget-title-fill-and-border .aft-posts-tabs-panel .nav-tabs>li>a.active,
.widget-title-fill-and-border .aft-main-banner-wrapper .widget-title .heading-line,
.widget-title-fill-and-no-border .wp_post_author_widget .widget-title .header-after,
.widget-title-fill-and-no-border .widget-title .heading-line,
.widget-title-fill-and-no-border .aft-posts-tabs-panel .nav-tabs>li>a.active,
.widget-title-fill-and-no-border .aft-main-banner-wrapper .widget-title .heading-line,

.aft-dark-mode .is-style-fill a.wp-block-button__link:not(.has-text-color),
.aft-default-mode .is-style-fill a.wp-block-button__link:not(.has-text-color),

div.wpforms-container-full button[type=submit]:hover,
div.wpforms-container-full button[type=submit]:not(:hover):not(:active),

body.aft-dark-mode .aft-popular-taxonomies-lists span,
body.aft-default-mode .aft-popular-taxonomies-lists span,
.af-post-format i,
.read-img .af-post-format i,
.af-youtube-slider .af-video-wrap .af-bg-play,
.af-youtube-slider .af-video-wrap .af-hide-iframe i,
.af-youtube-video-list .entry-header-yt-video-wrapper .af-yt-video-play i,
.woocommerce-product-search button[type="submit"],
input.search-submit,
body.aft-default-mode button,
body.aft-default-mode input[type="button"],
body.aft-default-mode input[type="reset"],
body.aft-default-mode input[type="submit"],
body.aft-dark-mode button,
body.aft-dark-mode input[type="button"],
body.aft-dark-mode input[type="reset"],
body.aft-dark-mode input[type="submit"],
body .trending-posts-vertical .trending-no,
body.aft-dark-mode .btn-style1 a,
body.aft-default-mode .btn-style1 a,
body.aft-dark-mode #scroll-up {
    color: #1A1A1A;
}

body.aft-default-mode #scroll-up::before,
body.aft-dark-mode #scroll-up::before {
    border-bottom-color: #1A1A1A;
}

a.sidr-class-sidr-button-close::before,
a.sidr-class-sidr-button-close::after {
    background-color: #1A1A1A;
}

.morenews-header .top-header,
footer.site-footer {
    background-color: #1A1A1A;
}

body .reply a,
div#respond input[type="submit"],
.btn-style1 a:visited,
.btn-style1 a,
button,
input[type="button"],
input[type="reset"],
input[type="submit"] {
    padding: 5px 10px;
    font-size: 14px;
    text-transform: uppercase;
    font-weight: 700;
}

.aft-default-mode .entry-content>.wp-block-heading a:not(.has-link-color) {
    border-color: #D1A74F;
}


@media screen and (max-width: 768px) {
    .entry-header .read-details .entry-meta .posts-author {
        display: block;
        margin-top: 20px;
    }
    .entry-header .read-details .entry-meta .posts-date {
        display: block;
        padding: 5px 0;
    }
}

/* =====================================================
   Gemini Color Theme Overrides (force apply)
   Palette: Red #D81E2C, Green #007833, Gold #D1A74F, Black #1A1A1A, White #FFFFFF, Brown #4A2C2A
   These use !important to override parent/customizer styles.
   ===================================================== */
:root {
    --cn-red: #D81E2C;
    --cn-green: #007833;
    --cn-gold: #D1A74F;
    --cn-brown: #4a2c2a;
    --cn-white: #FFFFFF;
    --cn-black: #1A1A1A;
    /* Light Theme Colors */
    --cn-light-bg: #f8f9fa;
    --cn-card-bg: #ffffff;
    --cn-text-dark: #2c3e50;
    --cn-text-light: #6c757d;
    --cn-border-light: #e9ecef;
    --cn-shadow-light: rgba(0, 0, 0, 0.1);
}

/* Light Theme - Primary surfaces */
body,
html,
.site,
#page {
    background-color: var(--cn-light-bg) !important;
    color: var(--cn-text-dark) !important;
}

.morenews-header .top-header,
footer.site-footer,
.site-footer {
    background-color: var(--cn-white) !important;
    color: var(--cn-text-dark) !important;
    border-top: 1px solid var(--cn-border-light) !important;
    box-shadow: 0 -2px 10px var(--cn-shadow-light) !important;
}

/* Navigation + toggles */
div#main-navigation-bar { background: var(--cn-gold) !important; }
body .main-navigation ul.menu>li>a,
body .main-navigation ul li a,
body .morenews-header .search-icon { color: var(--cn-black) !important; }
body .ham, body .ham:before, body .ham:after { background-color: var(--cn-black) !important; }
.header-layout-centered .main-navigation .toggle-menu a,
.header-layout-side .main-navigation .toggle-menu a,
.header-layout-compressed-full .main-navigation .toggle-menu a { outline-color: var(--cn-black) !important; }

/* Callouts / labels - Keep red for important alerts only */
.morenews-header div.custom-menu-link>a { background: var(--cn-red) !important; background-color: var(--cn-red) !important; }
.main-navigation .menu-description { background: var(--cn-green) !important; background-color: var(--cn-green) !important; }
.main-navigation .menu-description:after { border-top: 5px solid var(--cn-green) !important; }

/* Category colors */
.af-cat-widget-carousel a.morenews-categories.category-color-1 { background-color: var(--cn-green) !important; }
a.morenews-categories.category-color-1 { color: var(--cn-green) !important; }
.af-cat-widget-carousel a.morenews-categories.category-color-2 { background-color: var(--cn-gold) !important; }
a.morenews-categories.category-color-2 { color: var(--cn-gold) !important; }
.categories-inside-image a.morenews-categories.category-color-2 { color: var(--cn-black) !important; }

/* Accents */
.aft-default-mode .entry-content>.wp-block-heading a:not(.has-link-color) { border-color: var(--cn-gold) !important; }
body.aft-dark-mode #scroll-up { color: var(--cn-black) !important; }
body.aft-dark-mode #scroll-up::before { border-bottom-color: var(--cn-black) !important; }
a.sidr-class-sidr-button-close::before,
a.sidr-class-sidr-button-close::after { background-color: var(--cn-black) !important; }


/* Buttons - Modern gradient design */
button,
input[type="button"],
input[type="reset"],
input[type="submit"],
.wp-block-button .wp-block-button__link,
.wp-block-button__link,
.wc-block-grid .wp-block-button__link,
.woocommerce a.button,
.woocommerce button.button,
.woocommerce input.button,
.woocommerce #respond input#submit,
.btn-style1 a {
    background: linear-gradient(135deg, var(--cn-green), rgba(0, 120, 51, 0.8)) !important;
    color: var(--cn-white) !important;
    border: none !important;
    border-radius: 12px !important;
    padding: 12px 24px !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 16px rgba(0, 120, 51, 0.3) !important;
}

/* Button hovers - Smooth transitions */
button:hover,
input[type="button"]:hover,
input[type="reset"]:hover,
input[type="submit"]:hover,
.wp-block-button .wp-block-button__link:hover,
.wp-block-button__link:hover,
.wc-block-grid .wp-block-button__link:hover,
.woocommerce a.button:hover,
.woocommerce button.button:hover,
.woocommerce input.button:hover,
.woocommerce #respond input#submit:hover,
.btn-style1 a:hover {
    background: linear-gradient(135deg, var(--cn-gold), rgba(209, 167, 79, 0.9)) !important;
    color: var(--cn-black) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(209, 167, 79, 0.4) !important;
}

/* Search button - Special gold styling */
input.search-submit,
.woocommerce-product-search button[type="submit"] {
    background: linear-gradient(135deg, var(--cn-gold), rgba(209, 167, 79, 0.9)) !important;
    color: var(--cn-black) !important;
    border: none !important;
    border-radius: 12px !important;
    padding: 12px 20px !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 16px rgba(209, 167, 79, 0.3) !important;
    transition: all 0.3s ease !important;
}

input.search-submit:hover,
.woocommerce-product-search button[type="submit"]:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(209, 167, 79, 0.5) !important;
}

/* Global link colors */
a { color: var(--cn-gold); }
a:hover, a:focus { color: var(--cn-red); }

/* NO CARDS - Clean Flat Design */
.af-main-banner-wrapper,
.af-main-banner-wrapper .container-wrapper,
.morenews-customizer,
.morenews-customizer .container-wrapper,
.af-main-banner-latest-posts,
.af-main-banner-latest-posts.grid-layout .container-wrapper {
    background: transparent !important;
    border: none !important;
    border-radius: 0 !important;
    padding: 20px !important;
    margin-bottom: 24px !important;
    box-shadow: none !important;
    transition: none !important;
    color: var(--cn-text-dark) !important;
}

/* Remove card styling from inner articles to prevent double cards */
.af-main-banner-latest-posts .col-3 article,
.af-main-banner-latest-posts .col-4 article,
.af-container-row article,
.af-archive-wrapper article,
.site-main article,
.post {
    background: transparent !important;
    border: none !important;
    border-radius: 0 !important;
    padding: 20px 0 !important;
    margin-bottom: 20px !important;
    box-shadow: none !important;
    color: var(--cn-text-dark) !important;
}

/* NO HOVER EFFECTS - Clean Flat Design */
.af-main-banner-wrapper:hover,
.af-main-banner-wrapper .container-wrapper:hover,
.morenews-customizer:hover,
.morenews-customizer .container-wrapper:hover,
.af-main-banner-latest-posts:hover,
.af-main-banner-latest-posts.grid-layout .container-wrapper:hover,
.morenews-widget:hover,
.widget:hover,
.af-main-banner-latest-posts .col-3 article:hover,
.af-main-banner-latest-posts .col-4 article:hover,
.morenews-widget article:hover,
.widget article:hover,
.site-main article:hover {
    transform: none !important;
    box-shadow: none !important;
    border-color: transparent !important;
    background: transparent !important;
    border-radius: 0 !important;
}

/* Article titles and text - Better contrast */
.af-main-banner-latest-posts article h3,
.af-main-banner-latest-posts article h4,
.morenews-widget article h3,
.morenews-widget article h4,
.widget article h3,
.widget article h4,
.site-main article h1,
.site-main article h2,
.site-main article h3,
.entry-title,
.entry-title a {
    color: var(--cn-black) !important;
    font-weight: 600 !important;
    text-shadow: none !important;
}

/* Article content text - Better visibility */
.af-main-banner-latest-posts article p,
.morenews-widget article p,
.widget article p,
.site-main article p,
.entry-content,
.entry-summary {
    color: var(--cn-black) !important;
    font-weight: 400 !important;
    line-height: 1.6 !important;
}

/* Meta information (dates, authors) */
.entry-meta,
.entry-meta a,
.posts-date,
.posts-author,
.read-details .entry-meta {
    color: var(--cn-gold) !important;
}

/* NO CARDS - Clean Breaking News with Proper Padding */
.exclusive-posts,
.banner-exclusive-posts-wrapper {
    background: transparent !important;
    border: none !important;
    border-top: 3px solid var(--cn-green) !important;
    border-radius: 0 !important;
    margin: 32px 0 !important;
    padding: 24px 30px !important;
    box-shadow: none !important;
    overflow: visible !important;
    color: var(--cn-text-dark) !important;
}

.exclusive-posts .exclusive-now {
    background: linear-gradient(135deg, var(--cn-gold), rgba(209, 167, 79, 0.9)) !important;
    color: var(--cn-black) !important;
    border-radius: 8px !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
}
/* NO CARDS - Clean Widget Design */
.morenews-widget,
.widget,
.secondary .widget,
.af-widget {
    background: transparent !important;
    border: none !important;
    border-radius: 0 !important;
    padding: 20px 0 !important;
    margin-bottom: 32px !important;
    box-shadow: none !important;
    overflow: visible !important;
    color: var(--cn-text-dark) !important;
    border-bottom: 1px solid var(--cn-border-light) !important;
}

/* Clean spacing for widget content */
.widget-title,
.morenews-widget .widget-title {
    margin-bottom: 24px !important;
    padding-bottom: 12px !important;
    border-bottom: 2px solid var(--cn-border-light) !important;
    color: var(--cn-text-dark) !important;
    font-weight: 600 !important;
}

/* Clean list styling in widgets */
.widget ul,
.morenews-widget ul {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.widget li,
.morenews-widget li {
    padding: 12px 0 !important;
    border-bottom: 1px solid var(--cn-border-light) !important;
    margin: 0 !important;
}

.widget li:last-child,
.morenews-widget li:last-child {
    border-bottom: none !important;
}

/* Remove extra spacing from widget content */
.widget p,
.morenews-widget p {
    margin-bottom: 16px !important;
}

.widget p:last-child,
.morenews-widget p:last-child {
    margin-bottom: 0 !important;
}
/* Remove nested styling from widget articles */
.morenews-widget article,
.widget article {
    background: transparent !important;
    border: none !important;
    border-radius: 0 !important;
    padding: 16px 0 !important;
    margin-bottom: 16px !important;
    box-shadow: none !important;
    color: var(--cn-text-dark) !important;
}

/* Clean article spacing and typography */
.entry-content,
.post-content {
    line-height: 1.7 !important;
    margin-bottom: 24px !important;
}

.entry-title,
.post-title {
    margin-bottom: 16px !important;
    line-height: 1.3 !important;
}

.entry-meta,
.post-meta {
    margin-bottom: 20px !important;
    font-size: 14px !important;
}

/* Clean image styling */
.post-thumbnail,
.entry-thumbnail {
    margin-bottom: 20px !important;
    border-radius: 8px !important;
    overflow: hidden !important;
}

/* Remove any remaining dark backgrounds */
.af-main-banner-section,
.af-archive-wrapper,
.af-container-row,
.site-main,
.content-area {
    background: transparent !important;
    color: var(--cn-text-dark) !important;
}

/* Ensure proper spacing between sections */
.af-main-banner-section {
    margin-bottom: 40px !important;
}

.af-archive-wrapper,
.af-container-row {
    margin-bottom: 32px !important;
}

/* Widget titles - Modern header style */
.morenews-widget .widget-title,
.widget .widget-title,
.secondary .widget .widget-title,
.af-widget .widget-title {
    color: var(--cn-white) !important;
    background: linear-gradient(135deg, var(--cn-green), rgba(0, 120, 51, 0.8)) !important;
    padding: 16px 24px !important;
    margin: 0 0 20px 0 !important;
    border-radius: 0 !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    font-size: 14px !important;
}

/* Section titles - Clean modern headers */
.af-main-banner-wrapper .widget-title,
.morenews-customizer .widget-title,
.section-wrapper .widget-title {
    background: linear-gradient(135deg, var(--cn-green), rgba(0, 120, 51, 0.8)) !important;
    color: var(--cn-white) !important;
    padding: 16px 24px !important;
    border-radius: 16px !important;
    margin-bottom: 24px !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    box-shadow: 0 4px 16px rgba(0, 120, 51, 0.3) !important;
}

/* Widget content padding */
.morenews-widget .widget-content,
.widget > *:not(.widget-title),
.secondary .widget > *:not(.widget-title),
.af-widget > *:not(.widget-title) {
    padding: 0 24px 24px 24px !important;
}

/* Category tags and labels */
.morenews-categories,
.cat-links a,
.category-color-1,
.category-color-2 {
    border-radius: 4px !important;
    padding: 4px 8px !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    font-size: 11px !important;
}

/* Ensure body background is consistent */
body,
body.aft-dark-mode,
.site,
.site-content,
main.site-main {
    background-color: var(--cn-black) !important;
    color: var(--cn-white) !important;
}

/* Header area */
.morenews-header,
.af-middle-header,
.mid-header-wrapper {
    background-color: var(--cn-black) !important;
}

/* Site title and tagline */
.site-title,
.site-title a,
.site-description {
    color: var(--cn-white) !important;
}
/* Full-width layout improvements */
.container,
.container-wrapper,
.af-container-row {
    padding: 0 15px !important;
    max-width: 100% !important;
    margin: 0 !important;
    width: 100% !important;
}
/* Override theme's restrictive width settings */
.site-content,
.site-main,
.content-area,
.primary,
.secondary,
.widget-area {
    max-width: 100% !important;
    width: 100% !important;
    margin: 0 !important;
}

/* FORCE FULL-WIDTH LAYOUT - No Containers */
.af-main-banner-wrapper,
.morenews-customizer,
.af-main-banner-latest-posts,
.site-content-wrapper,
.main-wrapper,
.container,
.container-wrapper,
.site-content,
.primary,
.secondary {
    max-width: 100% !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    background: transparent !important;
    box-shadow: none !important;
}

/* Grid layouts should use full width */
.af-main-banner-latest-posts .row,
.morenews-customizer .row,
.af-container-row .row {
    margin: 0 !important;
    width: 100% !important;
}

/* Column spacing adjustments */
.af-main-banner-latest-posts .col-3,
.af-main-banner-latest-posts .col-4,
.af-main-banner-latest-posts .col-6,
.af-main-banner-latest-posts .col-8,
.af-main-banner-latest-posts .col-12 {
    padding-left: 8px !important;
    padding-right: 8px !important;
}

/* Remove harsh lines and improve spacing */
.af-main-banner-latest-posts,
.morenews-customizer,
.af-main-banner-wrapper {
    padding: 20px 0 !important;
}
/* Ensure body and html use full width */
body,
html {
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    overflow-x: hidden !important;
}

/* Site wrapper full width */
.site,
#page {
    max-width: 100% !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Header full width */
.site-header,
.header-wrapper {
    max-width: 100% !important;
    width: 100% !important;
    margin: 0 !important;
}

/* Footer full width */
.site-footer,
.footer-wrapper {
    max-width: 100% !important;
    width: 100% !important;
    margin: 0 !important;
}

/* Smooth category labels */
.morenews-categories,
.cat-links a {
    background: linear-gradient(135deg, var(--cn-green), rgba(0, 120, 51, 0.8)) !important;
    color: var(--cn-white) !important;
    border: none !important;
    border-radius: 20px !important;
    padding: 6px 12px !important;
    font-size: 11px !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    display: inline-block !important;
    margin: 4px 4px 4px 0 !important;
    transition: all 0.3s ease !important;
}

.morenews-categories:hover,
.cat-links a:hover {
    background: linear-gradient(135deg, var(--cn-gold), rgba(209, 167, 79, 0.9)) !important;
    color: var(--cn-black) !important;
    transform: translateY(-1px) !important;
}

/* Light Theme - Clean navigation bar */
div#main-navigation-bar {
    background: linear-gradient(135deg, var(--cn-gold), rgba(209, 167, 79, 0.9)) !important;
    border-radius: 0 !important;
    box-shadow: 0 2px 16px rgba(209, 167, 79, 0.2) !important;
    border-bottom: 1px solid var(--cn-border-light) !important;
}
/* Light Theme - Text and Typography */
h1, h2, h3, h4, h5, h6,
.entry-title,
.widget-title,
.site-title {
    color: var(--cn-text-dark) !important;
}

p, span, div, li, a {
    color: var(--cn-text-dark) !important;
}

/* Light links */
a:hover {
    color: var(--cn-green) !important;
}

/* Light Theme - Meta information */
.entry-meta,
.post-meta,
.comment-meta {
    color: var(--cn-text-light) !important;
}

/* Light Theme - Content areas */
.site-content,
.content-area,
.primary {
    background-color: transparent !important;
    color: var(--cn-text-dark) !important;
}

/* Light Theme - Sidebar */
.secondary,
.widget-area {
    background-color: transparent !important;
}

/* Light Theme - Form elements */
input[type="text"],
input[type="email"],
input[type="search"],
textarea,
select {
    background-color: var(--cn-card-bg) !important;
    border: 1px solid var(--cn-border-light) !important;
    color: var(--cn-text-dark) !important;
    border-radius: 8px !important;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="search"]:focus,
textarea:focus {
    border-color: var(--cn-green) !important;
    box-shadow: 0 0 0 2px rgba(0, 120, 51, 0.1) !important;
}

/* Smooth transitions for all interactive elements */
* {
    transition: all 0.3s ease !important;
}

/* Remove any remaining harsh borders */
.af-main-banner-latest-posts .col-3,
.af-main-banner-latest-posts .col-4,
.morenews-widget,
.widget,
.secondary .widget,
.af-widget,
article,
.post {
    border: none !important;
    outline: none !important;
}

/* CLEAN DESIGN WITH THIN GREEN BORDERS */
.af-main-banner-latest-posts *,
.af-main-banner-wrapper *,
.morenews-customizer *,
.morenews-widget *,
.widget *,
.af-widget *,
.secondary .widget *,
.af-container-row *,
.af-archive-wrapper *,
.site-main *,
.container-wrapper *,
.col-3 *,
.col-4 *,
.col-6 *,
.col-8 *,
.col-12 * {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    outline: none !important;
}

/* REMOVE ALL ARTICLE AND COLUMN BORDERS - Clean Layout */
article,
.post {
    border: none !important;
    border-radius: 0 !important;
    padding: 10px 0 !important;
    margin-bottom: 20px !important;
    background: transparent !important;
    width: 100% !important;
}

/* Remove column borders - Full width content */
.af-main-banner-latest-posts .col-3,
.af-main-banner-latest-posts .col-4,
.af-main-banner-latest-posts .col-6,
.af-main-banner-latest-posts .col-8,
.af-main-banner-latest-posts .col-12 {
    border: none !important;
    border-radius: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    background: transparent !important;
    width: 100% !important;
}

/* REMOVE ALL CARD CONTAINERS - Full Width Layout */
.af-main-banner-latest-posts,
.af-main-banner-wrapper,
.morenews-customizer {
    border: none !important;
    border-radius: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    background: transparent !important;
    width: 100% !important;
    max-width: 100% !important;
}

/* REMOVE WIDGET CARDS - Clean Layout */
.morenews-widget,
.widget,
.secondary .widget,
.af-widget {
    border: none !important;
    border-radius: 0 !important;
    padding: 15px 0 !important;
    margin-bottom: 25px !important;
    background: transparent !important;
    width: 100% !important;
}

/* NO HOVER EFFECTS - Completely Flat Design */
.af-main-banner-latest-posts:hover,
.af-main-banner-wrapper:hover,
.morenews-customizer:hover,
.morenews-widget:hover,
.widget:hover,
.secondary .widget:hover,
.af-widget:hover,
article:hover,
.post:hover,
.af-main-banner-latest-posts .col-3:hover,
.af-main-banner-latest-posts .col-4:hover,
.af-main-banner-latest-posts .col-6:hover,
.af-main-banner-latest-posts .col-8:hover,
.af-main-banner-latest-posts .col-12:hover {
    border: none !important;
    background: transparent !important;
    transition: none !important;
    transform: none !important;
    box-shadow: none !important;
}

/* SMART TEXT COLORS - Adapt to Background */

/* Default text for light backgrounds */
h1, h2, h3, h4, h5, h6,
.entry-title,
.widget-title,
.site-title {
    color: #1a1a1a !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    font-weight: 600 !important;
}

p, span, div, li {
    color: #333333 !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
}

/* Links for light backgrounds */
a {
    color: #007833 !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    font-weight: 500 !important;
}

a:hover {
    color: #d1a74f !important;
}
/* TEXT COLORS FOR DARK BACKGROUNDS */
.exclusive-posts,
.exclusive-posts h1,
.exclusive-posts h2,
.exclusive-posts h3,
.exclusive-posts h4,
.exclusive-posts h5,
.exclusive-posts h6,
.exclusive-posts p,
.exclusive-posts span,
.exclusive-posts div,
.exclusive-posts li,
.banner-exclusive-posts-wrapper,
.banner-exclusive-posts-wrapper h1,
.banner-exclusive-posts-wrapper h2,
.banner-exclusive-posts-wrapper h3,
.banner-exclusive-posts-wrapper h4,
.banner-exclusive-posts-wrapper h5,
.banner-exclusive-posts-wrapper h6,
.banner-exclusive-posts-wrapper p,
.banner-exclusive-posts-wrapper span,
.banner-exclusive-posts-wrapper div,
.banner-exclusive-posts-wrapper li,
.aft-dark-mode,
.aft-dark-mode h1,
.aft-dark-mode h2,
.aft-dark-mode h3,
.aft-dark-mode h4,
.aft-dark-mode h5,
.aft-dark-mode h6,
.aft-dark-mode p,
.aft-dark-mode span,
.aft-dark-mode div,
.aft-dark-mode li,
.site-footer,
.site-footer h1,
.site-footer h2,
.site-footer h3,
.site-footer h4,
.site-footer h5,
.site-footer h6,
.site-footer p,
.site-footer span,
.site-footer div,
.site-footer li {
    color: #ffffff !important;
    font-weight: 500 !important;
}

/* Ensure breaking news text is visible */
.exclusive-posts .exclusive-now {
    background: linear-gradient(135deg, var(--cn-gold), rgba(209, 167, 79, 0.9)) !important;
    color: var(--cn-black) !important;
    border-radius: 8px !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    padding: 8px 16px !important;
    margin-right: 15px !important;
}

/* Breaking news content text */
.exclusive-posts .exclusive-post,
.exclusive-posts .exclusive-post *,
.banner-exclusive-posts-wrapper .exclusive-post,
.banner-exclusive-posts-wrapper .exclusive-post * {
    color: var(--cn-white) !important;
    font-weight: 500 !important;
}

/* Article titles on dark backgrounds */
.exclusive-posts h1,
.exclusive-posts h2,
.exclusive-posts h3,
.exclusive-posts h4,
.exclusive-posts h5,
.exclusive-posts h6,
.banner-exclusive-posts-wrapper h1,
.banner-exclusive-posts-wrapper h2,
.banner-exclusive-posts-wrapper h3,
.banner-exclusive-posts-wrapper h4,
.banner-exclusive-posts-wrapper h5,
.banner-exclusive-posts-wrapper h6 {
    color: var(--cn-white) !important;
    font-weight: 600 !important;
}

/* Links on dark backgrounds */
.exclusive-posts a,
.banner-exclusive-posts-wrapper a,
.aft-dark-mode a,
.dark-bg a,
.site-footer a {
    color: var(--cn-gold) !important;
    font-weight: 500 !important;
}

.exclusive-posts a:hover,
.banner-exclusive-posts-wrapper a:hover,
.aft-dark-mode a:hover,
.dark-bg a:hover,
.site-footer a:hover {
    color: var(--cn-white) !important;
}
/* BODY WITH BACKGROUND IMAGE */
body {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    background: url('espa_bg.png') center center fixed !important;
    background-size: cover !important;
    background-repeat: no-repeat !important;
    overflow-x: hidden !important;
}

/* FORCE FULL PAGE WIDTH - Remove All Containers */
html,
.site,
.site-content,
.content-area,
.main-wrapper,
.af-main-banner-wrapper,
.af-main-banner-latest-posts,
.morenews-customizer,
.container,
.container-wrapper,
.row,
.col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
.col-7, .col-8, .col-9, .col-10, .col-11, .col-12 {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    background: transparent !important;
    box-shadow: none !important;
    border-radius: 0 !important;
}

/* Ensure content flows naturally */
.site-content {
    display: block !important;
    width: 100% !important;
}

.primary {
    width: 100% !important;
    float: none !important;
}

.secondary {
    width: 100% !important;
    float: none !important;
    clear: both !important;
}

/* Remove any Bootstrap or theme containers */
.container-fluid,
.container-lg,
.container-md,
.container-sm,
.container-xl {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* Ensure articles use full width */
article {
    width: 100% !important;
    display: block !important;
    margin: 0 0 20px 0 !important;
    padding: 15px !important;
}

/* Widget areas full width */
.widget-area {
    width: 100% !important;
    margin: 0 !important;
    padding: 15px !important;
}

/* Remove any grid constraints */
.grid,
.grid-layout,
.af-container-row {
    display: block !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
}
/* HEADER FIXES - Text Visibility and Padding */

/* Header text visibility */
.site-header,
.site-header *,
.morenews-header,
.morenews-header *,
.site-title,
.site-title a,
.site-description,
.main-navigation,
.main-navigation a,
.main-navigation ul li a,
.header-layout-side,
.header-layout-centered,
.header-layout-compressed-full {
    color: #1a1a1a !important;
    font-weight: 600 !important;
}

/* Header padding and spacing */
.site-header,
.morenews-header,
.af-top-header,
.af-middle-header,
.main-navigation,
.header-layout-side,
.header-layout-centered,
.header-layout-compressed-full {
    padding-left: 20px !important;
    padding-right: 20px !important;
    padding-top: 10px !important;
    padding-bottom: 10px !important;
}

/* Site title and description padding */
.site-title,
.site-description,
.site-branding {
    padding-left: 20px !important;
    margin-left: 0 !important;
}

/* Navigation menu padding */
.main-navigation ul,
.main-navigation ul li,
.main-navigation ul li a {
    padding-left: 15px !important;
    padding-right: 15px !important;
}

/* Search and other header elements */
.search-watch,
.search-icon,
.header-social-icons {
    padding-right: 20px !important;
}
/* CATEGORY TEXT VISIBILITY FIXES */

/* Category badges on green backgrounds - use white text */
.morenews-categories,
.category-color-1,
.category-color-2,
.af-cat-widget-carousel a.morenews-categories,
.categories-inside-image a.morenews-categories,
.post-categories a,
.cat-links a,
.entry-categories a {
    color: #ffffff !important;
    background-color: #007833 !important;
    font-weight: 600 !important;
    padding: 5px 12px !important;
    border-radius: 3px !important;
    text-decoration: none !important;
}

/* Category hover effects */
.morenews-categories:hover,
.category-color-1:hover,
.category-color-2:hover,
.post-categories a:hover,
.cat-links a:hover,
.entry-categories a:hover {
    background-color: #005a26 !important;
    color: #ffffff !important;
    transform: translateY(-1px) !important;
    transition: all 0.3s ease !important;
}

/* Section headers with green backgrounds */
.main-news-section-header,
.editors-picks-header,
.you-may-have-missed-header,
.section-header,
.widget-title {
    background-color: #007833 !important;
    color: #ffffff !important;
    padding: 12px 20px !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
    margin: 0 !important;
    border-radius: 5px !important;
}
/* MAIN NEWS SECTION HOVER EFFECTS */

/* Main news articles hover effects */
.af-main-banner-latest-posts article,
.main-news-section article,
.editors-picks-section article,
.you-may-have-missed-section article {
    transition: all 0.3s ease !important;
    padding: 15px !important;
    border-radius: 8px !important;
    margin-bottom: 15px !important;
}

.af-main-banner-latest-posts article:hover,
.main-news-section article:hover,
.editors-picks-section article:hover,
.you-may-have-missed-section article:hover {
    background-color: rgba(0, 120, 51, 0.1) !important;
    border-left: 4px solid #007833 !important;
    transform: translateX(5px) !important;
    box-shadow: 0 4px 12px rgba(0, 120, 51, 0.2) !important;
}

/* Article title hover effects */
.af-main-banner-latest-posts .entry-title a,
.main-news-section .entry-title a,
.editors-picks-section .entry-title a,
.you-may-have-missed-section .entry-title a {
    color: #1a1a1a !important;
    transition: color 0.3s ease !important;
}

.af-main-banner-latest-posts .entry-title a:hover,
.main-news-section .entry-title a:hover,
.editors-picks-section .entry-title a:hover,
.you-may-have-missed-section .entry-title a:hover {
    color: #007833 !important;
    text-decoration: none !important;
}

/* Read More links with green accent */
.read-more,
.read-more a,
.more-link,
.more-link a {
    color: #007833 !important;
    font-weight: 600 !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
}

.read-more:hover,
.read-more a:hover,
.more-link:hover,
.more-link a:hover {
    color: #d1a74f !important;
    transform: translateX(3px) !important;
}
/* DARK SECTION PADDING FIXES */

/* Dark background sections - add proper padding */
.exclusive-posts,
.banner-exclusive-posts-wrapper,
.breaking-news-section,
.featured-section,
.dark-bg,
.site-footer {
    padding: 20px !important;
    margin: 0 !important;
}

/* Dark section content padding */
.exclusive-posts .entry-title,
.exclusive-posts .entry-content,
.exclusive-posts .entry-meta,
.banner-exclusive-posts-wrapper .entry-title,
.banner-exclusive-posts-wrapper .entry-content,
.banner-exclusive-posts-wrapper .entry-meta,
.breaking-news-section .entry-title,
.breaking-news-section .entry-content,
.featured-section .entry-title,
.featured-section .entry-content {
    padding-left: 20px !important;
    padding-right: 20px !important;
    margin-left: 0 !important;
}

/* LIGHT BACKGROUND TEXT IMPROVEMENTS */

/* Main content text on light backgrounds */
.af-main-banner-latest-posts,
.main-news-section,
.editors-picks-section,
.you-may-have-missed-section,
.widget-area,
.sidebar {
    background-color: rgba(255, 255, 255, 0.9) !important;
    padding: 20px !important;
    margin-bottom: 20px !important;
    border-radius: 8px !important;
}

/* Text colors for light backgrounds */
.af-main-banner-latest-posts h1,
.af-main-banner-latest-posts h2,
.af-main-banner-latest-posts h3,
.af-main-banner-latest-posts h4,
.af-main-banner-latest-posts h5,
.af-main-banner-latest-posts h6,
.af-main-banner-latest-posts .entry-title,
.main-news-section h1,
.main-news-section h2,
.main-news-section h3,
.main-news-section .entry-title,
.editors-picks-section h1,
.editors-picks-section h2,
.editors-picks-section h3,
.editors-picks-section .entry-title,
.you-may-have-missed-section h1,
.you-may-have-missed-section h2,
.you-may-have-missed-section h3,
.you-may-have-missed-section .entry-title {
    color: #1a1a1a !important;
    font-weight: 700 !important;
}

/* Body text for light backgrounds */
.af-main-banner-latest-posts p,
.af-main-banner-latest-posts span,
.af-main-banner-latest-posts .entry-content,
.af-main-banner-latest-posts .entry-meta,
.main-news-section p,
.main-news-section span,
.main-news-section .entry-content,
.main-news-section .entry-meta,
.editors-picks-section p,
.editors-picks-section span,
.editors-picks-section .entry-content,
.you-may-have-missed-section p,
.you-may-have-missed-section span,
.you-may-have-missed-section .entry-content {
    color: #333333 !important;
    font-weight: 500 !important;
}
/* FINAL SPACING AND VISIBILITY FIXES */

/* Ensure all content has proper spacing from edges */
.site-content,
.primary,
.secondary,
.content-area {
    padding: 20px !important;
    margin: 0 !important;
}

/* Widget titles and content */
.widget-title,
.widget h1,
.widget h2,
.widget h3 {
    color: #ffffff !important;
    background-color: #007833 !important;
    padding: 12px 20px !important;
    margin: 0 0 15px 0 !important;
    border-radius: 5px !important;
    font-weight: 700 !important;
}

.widget-content,
.widget p,
.widget span,
.widget li {
    color: #333333 !important;
    padding: 10px 15px !important;
    background-color: rgba(255, 255, 255, 0.9) !important;
    border-radius: 5px !important;
}

/* Meta information styling */
.entry-meta,
.entry-meta a,
.post-meta,
.post-meta a,
.byline,
.posted-on {
    color: #666666 !important;
    font-size: 14px !important;
    font-weight: 500 !important;
}

.entry-meta a:hover,
.post-meta a:hover {
    color: #007833 !important;
}

/* Date and author information */
.entry-date,
.author,
.comments-link {
    color: #888888 !important;
    font-style: italic !important;
}

/* Ensure proper contrast for all links */
a {
    color: #007833 !important;
    text-decoration: none !important;
}

a:hover {
    color: #d1a74f !important;
    text-decoration: underline !important;
}